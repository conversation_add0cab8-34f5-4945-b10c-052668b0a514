import { NavigatorScreenParams, RouteProp } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Facility } from "../interfaces/facility";
import { Fine } from "../interfaces/fine";
import { Infraction } from "../interfaces/infraction";
import { Complaint } from "../interfaces/complaint";
import {
  PartialMaintenanceIssueReport,
  PartialProperty,
} from "../interfaces/me";

// Stack Parameter Lists
export type DashboardStackParamList = {
  Dashboard: undefined;
  CreateComplaint: { complaintId?: string };
  CreateMaintenanceReport: { reportId?: string };
  EmergencyNumbers: undefined;
};

export type FacilitiesStackParamList = {
  FacilitiesList: { isLoading?: boolean };
  FacilityDetail: { facility: Facility };
  CreateReservation: {
    id: Facility["id"];
    name: Facility["name"];
    maxAmountOfPeople: Facility["maxAmountOfPeople"];
    maxTimeOfStay: Facility["maxTimeOfStay"];
    open: Facility["open"];
    close: Facility["close"];
    selectedDate: string;
  };
};

export type PropertyStackParamList = {
  PropertyDetail: undefined;
  PropertyFines: { fine: Fine };
  PropertyInfractions: { infractions: Infraction[] };
  PropertyMaintenanceReports: {
    maintenanceIssueReports: PartialMaintenanceIssueReport[];
    property: PartialProperty;
  };
  PropertyComplaints: { complaints: Complaint[]; property: PartialProperty };
  PropertyTags: undefined;
  PropertyPets: undefined;
  PropertyVehicles: undefined;
  PropertyResidents: undefined;
  PropertyMonthlyCharges: { filterPaid?: boolean };
};

export type PaymentsStackParamList = {
  PaymentsList: undefined;
  PaymentDetail: { paymentId: string };
};

export type AccountStackParamList = {
  Profile: undefined;
  Settings: undefined;
  EditProfile: undefined;
};

// Main Tab Navigator Parameter List
export type MainTabParamList = {
  DashboardTab: NavigatorScreenParams<DashboardStackParamList>;
  FacilitiesTab: NavigatorScreenParams<FacilitiesStackParamList>;
  PropertyTab: NavigatorScreenParams<PropertyStackParamList>;
  PaymentsTab: NavigatorScreenParams<PaymentsStackParamList>;
  AccountTab: NavigatorScreenParams<AccountStackParamList>;
};

// Root Stack Parameter List
export type RootStackParamList = {
  Auth: undefined;
  Main: NavigatorScreenParams<MainTabParamList>;
};

// Specific Route Types
export type FacilityRouteProp = RouteProp<
  FacilitiesStackParamList,
  "FacilityDetail"
>;

export type CreateReservationRouteProp = RouteProp<
  FacilitiesStackParamList,
  "CreateReservation"
>;

export type PropertyComplaintsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyComplaints"
>;

export type PropertyMaintenanceReportsRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyMaintenanceReports"
>;

export type PropertyMonthlyChargesRouteProp = RouteProp<
  PropertyStackParamList,
  "PropertyMonthlyCharges"
>;

// Navigation Props
export type PropertyStackNavigationProp =
  NativeStackNavigationProp<PropertyStackParamList>;
export type FacilitiesStackNavigationProp =
  NativeStackNavigationProp<FacilitiesStackParamList>;
export type DashboardStackNavigationProp =
  NativeStackNavigationProp<DashboardStackParamList>;
export type PaymentsStackNavigationProp =
  NativeStackNavigationProp<PaymentsStackParamList>;
export type AccountStackNavigationProp =
  NativeStackNavigationProp<AccountStackParamList>;
