import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { PropertyScreen } from "../../screens/Property/PropertyScreen";
import { PropertyStackParamList } from "../types";
import { PROPERTY_SCREENS } from "../constants";
import { InfractionsScreen } from "../../screens/Infractions/InfractionScreen";
import { PropertyComplaintsScreen } from "../../screens/Property/PropertyComplaintsScreen";
import { PropertyMaintenanceReportsScreen } from "../../screens/Property/PropertyMaintenanceReportsScreen";
import { PropertyMonthlyChargesScreen } from "../../screens/Property/PropertyMonthlyChargesScreen";

const Stack = createNativeStackNavigator<PropertyStackParamList>();

export const PropertyStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_DETAIL}
        component={PropertyScreen}
        options={{
          title: "Mi Propiedad",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_INFRACTIONS}
        component={InfractionsScreen}
        options={{
          title: "Multas e Infracciones",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_COMPLAINTS}
        component={PropertyComplaintsScreen}
        options={{
          title: "Quejas",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_MAINTENANCE_REPORTS}
        component={PropertyMaintenanceReportsScreen}
        options={{
          title: "Reportes de Mantenimiento",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_MONTHLY_CHARGES}
        component={PropertyMonthlyChargesScreen}
        options={{
          title: "Cargos Mensuales",
        }}
      />
    </Stack.Navigator>
  );
};

// Export for backward compatibility
export const PropertiesStack = PropertyStack;
