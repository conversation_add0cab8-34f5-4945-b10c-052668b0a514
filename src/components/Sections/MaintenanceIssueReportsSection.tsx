import { StyleSheet, Text, TouchableOpacity } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Card, Section, Row, Col } from "../main";
import {
  PartialMaintenanceIssueReport,
  PartialProperty,
} from "../../interfaces/me";
import { theme } from "../../theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";

interface MaintenanceIssueReportsSectionProps {
  maintenanceIssueReports: PartialMaintenanceIssueReport[];
  property: PartialProperty;
}

export const MaintenanceIssueReportsSection: React.FC<
  MaintenanceIssueReportsSectionProps
> = ({ maintenanceIssueReports, property }) => {
  const navigation = useNavigation();

  if (!maintenanceIssueReports.length) {
    return (
      <Section title="Reportes de mantenimiento">
        <Text style={styles.noDataText}>No hay reportes de mantenimiento</Text>
      </Section>
    );
  }

  // Separar por estado
  const openReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("abierto") ||
      r.status.toLowerCase().includes("open")
  );
  const inProgressReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("progreso") ||
      r.status.toLowerCase().includes("progress")
  );
  const resolvedReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("resuelto") ||
      r.status.toLowerCase().includes("resolved") ||
      r.status.toLowerCase().includes("completado")
  );

  return (
    <Section title="Reportes de mantenimiento">
      <Card style={styles.summaryCard}>
        <Row align="center" style={styles.summaryRow}>
          <Col justify="center" align="center">
            <MaterialCommunityIcons
              name="clock-outline"
              size={24}
              color={theme.colors.primary}
            />
            <Text style={styles.summaryNumber}>{openReports.length}</Text>
            <Text style={styles.summaryLabel}>Abiertos</Text>
          </Col>

          <Col justify="center" align="center">
            <MaterialCommunityIcons
              name="clock-fast"
              size={24}
              color={theme.colors.warning}
            />
            <Text style={styles.summaryNumber}>{inProgressReports.length}</Text>
            <Text style={styles.summaryLabel}>En Progreso</Text>
          </Col>

          <Col justify="center" align="center">
            <MaterialCommunityIcons
              name="check-circle"
              size={24}
              color={theme.colors.success}
            />
            <Text style={styles.summaryNumber}>{resolvedReports.length}</Text>
            <Text style={styles.summaryLabel}>Resueltos</Text>
          </Col>
        </Row>

        <TouchableOpacity
          style={styles.viewAllRow}
          onPress={() =>
            navigation.navigate(PROPERTY_SCREENS.PROPERTY_MAINTENANCE_REPORTS, {
              maintenanceIssueReports,
              property,
            })
          }
          activeOpacity={0.7}
        >
          <Row align="center" style={styles.viewAllRow}>
            <Text style={styles.viewAllText}>Ver todos los reportes</Text>
            <MaterialCommunityIcons
              name="chevron-right"
              size={16}
              color={theme.colors.primary}
            />
          </Row>
        </TouchableOpacity>
      </Card>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  viewAllRow: {
    justifyContent: "center",
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  viewAllText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },

  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
