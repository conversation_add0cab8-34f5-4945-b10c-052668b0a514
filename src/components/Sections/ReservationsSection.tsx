import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { PartialReservation } from "../../interfaces/me";
import { ReservationStatus } from "../../interfaces/reservation";
import { Card, Row, Section, Col } from "../main";
import {
  formatDateDMY,
  getStringTimeFromIso,
} from "../../utils/date-time.utils";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme/theme";
import { getReservationStatusData } from "../../utils/convertions";

interface ReservationsSectionProps {
  reservations: PartialReservation[];
}

const isReservationToday = (startDateTime: string): boolean => {
  const today = new Date();
  const reservationDate = new Date(startDateTime);
  return (
    today.getDate() === reservationDate.getDate() &&
    today.getMonth() === reservationDate.getMonth() &&
    today.getFullYear() === reservationDate.getFullYear()
  );
};

export const ReservationsSection: React.FC<ReservationsSectionProps> = ({
  reservations,
}) => {
  if (!reservations?.length) {
    return (
      <Section title="Reservaciones">
        <Text style={styles.noDataText}>No hay reservaciones registradas</Text>
      </Section>
    );
  }

  // Separar por estado
  const pendingReservations = reservations.filter(
    (r) => r.status === ReservationStatus.PENDING
  );
  const approvedReservations = reservations.filter(
    (r) => r.status === ReservationStatus.APPROVED
  );
  const rejectedReservations = reservations.filter(
    (r) => r.status === ReservationStatus.REJECTED
  );

  // Ordenar por fecha más próxima
  const sortedReservations = [...reservations].sort(
    (a, b) =>
      new Date(a.startDateTime).getTime() - new Date(b.startDateTime).getTime()
  );

  return (
    <Section title="Reservaciones">
      <Col>
        {/* Resumen de estados */}
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="clock-outline"
                size={20}
                color={theme.colors.warning}
              />
              <Text style={styles.summaryNumber}>
                {pendingReservations.length}
              </Text>
              <Text style={styles.summaryLabel}>Pendientes</Text>
            </View>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color={theme.colors.success}
              />
              <Text style={styles.summaryNumber}>
                {approvedReservations.length}
              </Text>
              <Text style={styles.summaryLabel}>Aprobadas</Text>
            </View>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="close-circle"
                size={20}
                color={theme.colors.error}
              />
              <Text style={styles.summaryNumber}>
                {rejectedReservations.length}
              </Text>
              <Text style={styles.summaryLabel}>Rechazadas</Text>
            </View>
          </Row>
        </Card>

        {/* Lista de reservaciones */}
        {sortedReservations.slice(0, 5).map((reservation) => {
          const isToday = isReservationToday(reservation.startDateTime);

          const reservationStatusData = getReservationStatusData(
            reservation.status
          );

          return (
            <TouchableOpacity
              key={reservation.id}
              onPress={() =>
                console.log("Pressed reservation:", reservation.id)
              }
              activeOpacity={0.7}
            >
              <Card
                style={[styles.reservationCard, isToday && styles.todayCard]}
              >
                <Row align="flex-start">
                  <View
                    style={[
                      styles.facilityIndicator,
                      { backgroundColor: `${theme.colors.primary}20` },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={reservationStatusData.icon}
                      size={24}
                      color={theme.colors.primary}
                    />
                  </View>
                  <Col style={styles.reservationInfo}>
                    <Row align="center" style={styles.headerRow}>
                      <Text style={styles.facilityName} numberOfLines={1}>
                        {reservation.facility.name}
                      </Text>
                      <View
                        style={[
                          styles.statusChip,
                          {
                            backgroundColor: reservationStatusData.color,
                          },
                        ]}
                      >
                        <MaterialCommunityIcons
                          name={reservationStatusData.icon}
                          size={12}
                          color={theme.colors.white}
                        />
                        <Text style={styles.statusText}>
                          {reservationStatusData.label}
                        </Text>
                      </View>
                    </Row>

                    <Row align="center" style={styles.detailRow}>
                      <MaterialCommunityIcons
                        name="calendar"
                        size={16}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.dateText}>
                        {formatDateDMY(reservation.startDateTime)}
                        {isToday && (
                          <Text style={styles.todayText}> (Hoy)</Text>
                        )}
                      </Text>
                    </Row>

                    <Row align="center" style={styles.detailRow}>
                      <MaterialCommunityIcons
                        name="clock"
                        size={16}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.timeText}>
                        {getStringTimeFromIso(reservation.startDateTime)} -{" "}
                        {getStringTimeFromIso(reservation.endDateTime)}
                      </Text>
                    </Row>

                    <Row align="center" style={styles.detailRow}>
                      <MaterialCommunityIcons
                        name="account-multiple"
                        size={16}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.peopleText}>
                        {reservation.amountOfPeople}{" "}
                        {reservation.amountOfPeople === 1
                          ? "persona"
                          : "personas"}
                      </Text>
                    </Row>

                    {reservation.deniedReason && (
                      <View style={styles.deniedReasonContainer}>
                        <MaterialCommunityIcons
                          name="information"
                          size={14}
                          color={theme.colors.error}
                        />
                        <Text style={styles.deniedReasonText} numberOfLines={2}>
                          {reservation.deniedReason}
                        </Text>
                      </View>
                    )}
                  </Col>
                  <MaterialCommunityIcons
                    name="chevron-right"
                    size={theme.fontSizes.lg}
                    color={theme.colors.gray500}
                  />
                </Row>
              </Card>
            </TouchableOpacity>
          );
        })}

        {reservations.length > 5 && (
          <Text style={styles.moreItemsText}>
            Y {reservations.length - 5} reservaciones más...
          </Text>
        )}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "row",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  reservationCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  todayCard: {
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
    backgroundColor: `${theme.colors.primary}05`,
  },
  facilityIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  reservationInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  facilityName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  detailRow: {
    marginBottom: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  todayText: {
    fontWeight: "600",
    color: theme.colors.primary,
  },
  timeText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  peopleText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  deniedReasonContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginTop: theme.spacing.sm,
    padding: theme.spacing.sm,
    backgroundColor: `${theme.colors.error}10`,
    borderRadius: theme.radii.sm,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.error,
  },
  deniedReasonText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.error,
    marginLeft: 6,
    flex: 1,
    lineHeight: 16,
  },
  moreItemsText: {
    textAlign: "center",
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    fontStyle: "italic",
    marginTop: theme.spacing.sm,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
