import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { Infraction, InfractionSeverity } from "../../interfaces/infraction";
import { Card } from "../main/Card";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { Section } from "../main/Section";
import { formatDateDMY } from "../../utils/date-time.utils";
import { getSeverityData } from "../../utils/convertions";

interface InfractionsSectionProps {
  infractions: Infraction[];
  onViewAll?: () => void;
}

export const InfractionsSection: React.FC<InfractionsSectionProps> = ({
  infractions,
  onViewAll,
}) => {
  // Todos los cálculos arriba del return
  const severeInfractions = infractions.filter(
    (i) => i.severity === InfractionSeverity.SEVERE
  );
  const moderateInfractions = infractions.filter(
    (i) => i.severity === InfractionSeverity.MODERATE
  );
  const minorInfractions = infractions.filter(
    (i) => i.severity === InfractionSeverity.MINOR
  );

  // Haz el sort aquí para no mutar el array en JSX
  const sortedInfractions = [...infractions].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Return condicional DESPUÉS de los cálculos
  if (!infractions.length) {
    return (
      <Section title="Infracciones">
        <Text style={styles.noDataText}>No hay infracciones registradas</Text>
      </Section>
    );
  }

  return (
    <Section title="Infracciones">
      <Col>
        {/* Resumen de infracciones */}
        <TouchableOpacity onPress={onViewAll} activeOpacity={0.7}>
          <Card style={styles.summaryCard}>
            <Row align="center" style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <MaterialCommunityIcons
                  name="alert-octagon"
                  size={24}
                  color={theme.colors.error}
                />
                <Text style={styles.summaryNumber}>
                  {severeInfractions.length}
                </Text>
                <Text style={styles.summaryLabel}>Graves</Text>
              </View>
              <View style={styles.summaryItem}>
                <MaterialCommunityIcons
                  name="alert"
                  size={24}
                  color={theme.colors.warning}
                />
                <Text style={styles.summaryNumber}>
                  {moderateInfractions.length}
                </Text>
                <Text style={styles.summaryLabel}>Moderadas</Text>
              </View>
              <View style={styles.summaryItem}>
                <MaterialCommunityIcons
                  name="information"
                  size={24}
                  color={theme.colors.success}
                />
                <Text style={styles.summaryNumber}>
                  {minorInfractions.length}
                </Text>
                <Text style={styles.summaryLabel}>Menores</Text>
              </View>
            </Row>

            <Row align="center" style={styles.viewAllRow}>
              <Text style={styles.viewAllText}>Ver todas las infracciones</Text>
              <MaterialCommunityIcons
                name="chevron-right"
                size={16}
                color={theme.colors.primary}
              />
            </Row>
          </Card>
        </TouchableOpacity>

        {/* Mostrar infracciones más recientes */}
        <View style={styles.recentInfractionsContainer}>
          <Text style={styles.recentInfractionsTitle}>
            Infracciones recientes
          </Text>
          {sortedInfractions.slice(0, 3).map((infraction) => {
            const severityData = getSeverityData(infraction.severity);
            return (
              <Card key={infraction.id} style={styles.infractionCard}>
                <Row align="flex-start">
                  <View
                    style={[
                      styles.severityIndicator,
                      {
                        backgroundColor: `${severityData.color}20`,
                      },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={severityData.icon}
                      size={20}
                      color={severityData.color}
                    />
                  </View>
                  <Col style={styles.infractionInfo}>
                    <Row align="center" style={styles.headerRow}>
                      <Text
                        style={styles.infractionDescription}
                        numberOfLines={2}
                      >
                        {infraction.description}
                      </Text>
                      <View
                        style={[
                          styles.severityBadge,
                          {
                            backgroundColor: severityData.color,
                          },
                        ]}
                      >
                        <Text style={styles.severityText}>
                          {severityData.label}
                        </Text>
                      </View>
                    </Row>
                    <Text style={styles.infractionDate}>
                      Fecha: {formatDateDMY(infraction.date)}
                    </Text>
                  </Col>
                </Row>
              </Card>
            );
          })}
        </View>
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  viewAllRow: {
    justifyContent: "center",
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  viewAllText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },
  recentInfractionsContainer: {
    marginTop: theme.spacing.sm,
  },
  recentInfractionsTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  infractionCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  severityIndicator: {
    width: 40,
    height: 40,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  infractionInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.xs,
  },
  infractionDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.black,
    lineHeight: 18,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  severityBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  severityText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    textTransform: "uppercase",
  },
  infractionDate: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
