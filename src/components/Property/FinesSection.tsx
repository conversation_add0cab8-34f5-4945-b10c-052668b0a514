import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { Fine } from "../../interfaces/fine";
import { Card } from "../main/Card";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { Section } from "../main/Section";
import { formatDateDMY } from "../../utils/date-time.utils";

interface FinesSectionProps {
  fines: Fine[];
  onViewAll?: () => void;
}

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("es-MX", {
    style: "currency",
    currency: "MXN",
  }).format(amount);
};

export const FinesSection: React.FC<FinesSectionProps> = ({
  fines,
  onViewAll,
}) => {
  if (!fines.length) {
    return (
      <Section title="Multas">
        <Text style={styles.noDataText}>No hay multas registradas</Text>
      </Section>
    );
  }

  // Separar por estado de pago
  const unpaidFines = fines.filter((fine) => !fine.isPaid);
  const paidFines = fines.filter((fine) => fine.isPaid);

  // Calcular total adeudado
  const totalOwed = unpaidFines.reduce((sum, fine) => sum + fine.amount, 0);

  return (
    <Section title="Multas">
      <Col>
        {/* Resumen de multas */}
        <TouchableOpacity onPress={onViewAll} activeOpacity={0.7}>
          <Card style={styles.summaryCard}>
            <Row align="center" style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <MaterialCommunityIcons
                  name="alert-circle"
                  size={24}
                  color={theme.colors.error}
                />
                <Text style={styles.summaryNumber}>{fines.length}</Text>
                <Text style={styles.summaryLabel}>Total</Text>
              </View>
              <View style={styles.summaryItem}>
                <MaterialCommunityIcons
                  name="close-circle"
                  size={24}
                  color={theme.colors.warning}
                />
                <Text style={styles.summaryNumber}>{unpaidFines.length}</Text>
                <Text style={styles.summaryLabel}>Pendientes</Text>
              </View>
              <View style={styles.summaryItem}>
                <MaterialCommunityIcons
                  name="check-circle"
                  size={24}
                  color={theme.colors.success}
                />
                <Text style={styles.summaryNumber}>{paidFines.length}</Text>
                <Text style={styles.summaryLabel}>Pagadas</Text>
              </View>
            </Row>

            {totalOwed > 0 && (
              <View style={styles.totalOwedContainer}>
                <Text style={styles.totalOwedLabel}>Total adeudado:</Text>
                <Text style={styles.totalOwedAmount}>
                  {formatCurrency(totalOwed)}
                </Text>
              </View>
            )}

            <Row align="center" style={styles.viewAllRow}>
              <Text style={styles.viewAllText}>Ver todas las multas</Text>
              <MaterialCommunityIcons
                name="chevron-right"
                size={16}
                color={theme.colors.primary}
              />
            </Row>
          </Card>
        </TouchableOpacity>

        {/* Mostrar multas pendientes más recientes */}
        {unpaidFines.length > 0 && (
          <View style={styles.recentFinesContainer}>
            <Text style={styles.recentFinesTitle}>
              Multas pendientes recientes
            </Text>
            {unpaidFines.slice(0, 2).map((fine) => (
              <Card key={fine.id} style={styles.fineCard}>
                <Row align="flex-start">
                  <View style={styles.fineIndicator}>
                    <MaterialCommunityIcons
                      name="alert-circle"
                      size={20}
                      color={theme.colors.error}
                    />
                  </View>
                  <Col style={styles.fineInfo}>
                    <Text style={styles.fineAmount}>
                      {formatCurrency(fine.amount)}
                    </Text>
                    <Text style={styles.fineDescription} numberOfLines={2}>
                      {fine.description}
                    </Text>
                    <Text style={styles.fineDate}>
                      Emitida: {formatDateDMY(fine.issuedAt)}
                    </Text>
                  </Col>
                  <View style={styles.statusBadge}>
                    <Text style={styles.statusText}>PENDIENTE</Text>
                  </View>
                </Row>
              </Card>
            ))}
          </View>
        )}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  totalOwedContainer: {
    backgroundColor: `${theme.colors.error}10`,
    borderRadius: theme.radii.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.error,
  },
  totalOwedLabel: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.error,
    fontWeight: "600",
  },
  totalOwedAmount: {
    fontSize: theme.fontSizes.xl,
    color: theme.colors.error,
    fontWeight: "700",
    marginTop: 2,
  },
  viewAllRow: {
    justifyContent: "center",
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  viewAllText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },
  recentFinesContainer: {
    marginTop: theme.spacing.sm,
  },
  recentFinesTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  fineCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  fineIndicator: {
    marginRight: theme.spacing.md,
    marginTop: 2,
  },
  fineInfo: {
    flex: 1,
  },
  fineAmount: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.error,
    marginBottom: 2,
  },
  fineDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.black,
    lineHeight: 18,
    marginBottom: theme.spacing.xs,
  },
  fineDate: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
  },
  statusBadge: {
    backgroundColor: theme.colors.warning,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
