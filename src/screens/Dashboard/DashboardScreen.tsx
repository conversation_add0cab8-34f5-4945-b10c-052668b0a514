import React, { useState } from "react";
import { StyleSheet, Text } from "react-native";
import { getDateFromJSDate, getGreeting } from "../../utils/date-time.utils";
import { AnnouncementSection } from "../../components/Sections/AnnouncementSection";
import { ReservationsSection } from "../../components/Sections/ReservationsSection";
import { QuickActionsSection } from "../../components/Sections/QuickActionsSection";
import { theme } from "../../theme";
import { useAuthContext } from "../../context/AuthContext";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me, PartialAnnouncement } from "../../interfaces/me";
import { useEntityModal } from "../../hooks/useEntityModal";
import { InfoModal } from "../../components/modals/InfoModal";
import { InfoList } from "../../components/InfoList";
import { InfoWithImage } from "../../components/InfoWithImage";
import { GradientView } from "../../components/layouts/GradientView";
import { FontAwesome } from "@expo/vector-icons";
import { QUERIES } from "../../constants/queries";
import { PhoneDirectory } from "../../interfaces/phone-directory";
import { handleCall } from "../../utils/phone-call";

export const DashboardScreen: React.FC = () => {
  const { user, isLoading: isAuthLoading } = useAuthContext();
  const { isOpen, openModal, closeModal } = useEntityModal();
  const { data: phoneDirectory } = useCachedQuery<PhoneDirectory[]>(
    QUERIES.PHONE_DIRECTORY
  );
  const [selectedAnnouncement, setSelectedAnnouncement] =
    useState<PartialAnnouncement | null>(null);
  const [isEmergencyInfo, setIsEmergencyInfo] = useState(false);

  const { data: userData, error } = useCachedQuery<Me>(QUERIES.ME, {
    enabled: !!user?.sub && !isAuthLoading,
  });

  console.log(userData);

  if (error) {
    return <Text>Error</Text>;
  }
  if (!userData) {
    return <Text>Sin datos</Text>;
  }

  const { firstName, announcements, requestedReservations } = userData;

  const handleEmergencyInfoPress = () => {
    setIsEmergencyInfo(true);
    openModal();
  };

  const handleAnnouncementPress = (announcement: PartialAnnouncement) => {
    setSelectedAnnouncement(announcement);
    setIsEmergencyInfo(false);
    openModal();
  };

  return (
    <GradientView
      firstLineText={getGreeting()}
      secondLineText={firstName ?? ""}
      icon={
        <FontAwesome
          name="phone-square"
          size={theme.fontSizes.xxl}
          color={theme.colors.orange.light}
          style={styles.icon}
        />
      }
      action={handleEmergencyInfoPress}
    >
      <AnnouncementSection
        announcements={announcements}
        onAnnouncementPress={handleAnnouncementPress}
      />

      <ReservationsSection reservations={requestedReservations} />

      <QuickActionsSection />

      <InfoModal
        onClose={closeModal}
        visible={isOpen}
        title={isEmergencyInfo ? "Directorio telefonico" : "Comunicado"}
      >
        {isEmergencyInfo ? (
          <InfoList
            items={
              phoneDirectory?.map((item) => {
                return {
                  title: item.name,
                  subtitle: item.phoneNumber,
                  onPress: () => handleCall(item.phoneNumber),
                };
              }) || []
            }
          />
        ) : (
          selectedAnnouncement && (
            <InfoWithImage
              imageUrl={selectedAnnouncement.images[0].path ?? ""}
              title={selectedAnnouncement.title ?? ""}
              description={selectedAnnouncement.message ?? ""}
              pillText={getDateFromJSDate(selectedAnnouncement.createdAt)}
            />
          )
        )}
      </InfoModal>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  icon: {
    paddingTop: theme.spacing.sm,
    position: "absolute",
    right: theme.spacing.sm,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: "10%",
  },
  headerText: {
    color: theme.colors.white,
  },
  body: {
    flex: 1,
    backgroundColor: "white",
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    overflow: "hidden",
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
    elevation: 5,
    marginBottom: 30,
  },
  scrollContent: {
    paddingBottom: theme.spacing.xl,
  },
  content: {
    flexGrow: 1,
  },
});
