import { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { Status } from "../../interfaces/maintenance-issue-report";
import { formatDateDMY } from "../../utils/date-time.utils";
import { Loading } from "../../components/Loading";
import { PropertyComplaintsRouteProp } from "../../navigation/types";
import { useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import {
  getPriorityColor,
  getPriorityIcon,
  getPriorityLabel,
  getStatusColor,
  getStatusIcon,
  getStatusLabel,
} from "../../utils/convertions";

export const PropertyComplaintsScreen: React.FC = () => {
  const route = useRoute<PropertyComplaintsRouteProp>();
  const { complaints, property } = route.params || {};

  const [selectedFilter, setSelectedFilter] = useState<Status | "ALL">(
    Status.OPEN
  );

  const { data: userData, isLoading } = useCachedQuery<Me>(`mobile/me`);

  // Move hooks above early returns to avoid conditional hook calls

  // Filtrar quejas según el filtro seleccionado
  const filteredComplaints = useMemo(() => {
    if (selectedFilter === "ALL") return complaints;
    return complaints.filter(
      (complaint) => complaint.status === selectedFilter
    );
  }, [complaints, selectedFilter]);

  if (isLoading) return <Loading />;
  if (!userData) return <Text>Sin datos</Text>;

  // Estadísticas
  const openComplaints = complaints.filter((c) => c.status === Status.OPEN);
  const inProgressComplaints = complaints.filter(
    (c) => c.status === Status.IN_PROGRESS
  );
  const resolvedComplaints = complaints.filter(
    (c) => c.status === Status.RESOLVED
  );

  const filterOptions = [
    {
      key: "ALL",
      label: "Todas",
      count: complaints.length,
      color: theme.colors.gray500,
    },
    {
      key: Status.OPEN,
      label: "Abiertas",
      count: openComplaints.length,
      color: theme.colors.primary,
    },
    {
      key: Status.IN_PROGRESS,
      label: "En Progreso",
      count: inProgressComplaints.length,
      color: theme.colors.warning,
    },
    {
      key: Status.RESOLVED,
      label: "Resueltas",
      count: resolvedComplaints.length,
      color: theme.colors.success,
    },
  ];

  return (
    <GradientView
      firstLineText="Quejas"
      secondLineText={property?.address ?? "Mi Propiedad"}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(Status.OPEN)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="clock-outline"
                  size={24}
                  color={theme.colors.primary}
                />
                <Text style={styles.summaryNumber}>
                  {openComplaints.length}
                </Text>
                <Text style={styles.summaryLabel}>Abiertas</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(Status.IN_PROGRESS)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="clock-fast"
                  size={24}
                  color={theme.colors.warning}
                />
                <Text style={styles.summaryNumber}>
                  {inProgressComplaints.length}
                </Text>
                <Text style={styles.summaryLabel}>En Progreso</Text>
              </TouchableOpacity>
            </Col>

            <Col>
              <TouchableOpacity
                onPress={() => setSelectedFilter(Status.RESOLVED)}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="check-circle"
                  size={24}
                  color={theme.colors.success}
                />
                <Text style={styles.summaryNumber}>
                  {resolvedComplaints.length}
                </Text>
                <Text style={styles.summaryLabel}>Resueltas</Text>
              </TouchableOpacity>
            </Col>
            <Col>
              <TouchableOpacity
                onPress={() => () => setSelectedFilter("ALL")}
                activeOpacity={0.7}
                style={styles.summaryItem}
              >
                <MaterialCommunityIcons
                  name="view-list-outline"
                  size={24}
                  color={theme.colors.success}
                />
                <Text style={styles.summaryNumber}>
                  {resolvedComplaints.length}
                </Text>
                <Text style={styles.summaryLabel}>Todas</Text>
              </TouchableOpacity>
            </Col>
          </Row>
        </Card>

        {/* Lista de quejas */}
        {filteredComplaints.length === 0 ? (
          <Card style={styles.noDataCard}>
            <Text style={styles.noDataText}>
              {selectedFilter === "ALL"
                ? "No hay quejas registradas"
                : `No hay quejas ${filterOptions
                    .find((f) => f.key === selectedFilter)
                    ?.label.toLowerCase()}`}
            </Text>
          </Card>
        ) : (
          filteredComplaints.map((complaint) => (
            <TouchableOpacity
              key={complaint.id}
              onPress={() => console.log("Ver detalle de queja:", complaint.id)}
              activeOpacity={0.7}
            >
              <Card style={styles.complaintCard}>
                <Row align="flex-start">
                  <View
                    style={[
                      styles.priorityIndicator,
                      {
                        backgroundColor: `${getPriorityColor(
                          complaint.priority
                        )}20`,
                      },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={getPriorityIcon(complaint.priority)}
                      size={24}
                      color={getPriorityColor(complaint.priority)}
                    />
                  </View>
                  <Col style={styles.complaintInfo}>
                    <Row align="center" style={styles.headerRow}>
                      <View
                        style={[
                          styles.priorityChip,
                          {
                            backgroundColor: getPriorityColor(
                              complaint.priority
                            ),
                          },
                        ]}
                      >
                        <Text style={styles.priorityText}>
                          {getPriorityLabel(complaint.priority)}
                        </Text>
                      </View>
                      <View
                        style={[
                          styles.statusChip,
                          { backgroundColor: getStatusColor(complaint.status) },
                        ]}
                      >
                        <MaterialCommunityIcons
                          name={getStatusIcon(complaint.status)}
                          size={12}
                          color={theme.colors.white}
                        />
                        <Text style={styles.statusText}>
                          {getStatusLabel(complaint.status)}
                        </Text>
                      </View>
                    </Row>
                    <Text style={styles.complaintDetail} numberOfLines={3}>
                      {complaint.detail}
                    </Text>
                    <Row align="center" style={styles.dateRow}>
                      <MaterialCommunityIcons
                        name="calendar"
                        size={14}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.dateText}>
                        Creada: {formatDateDMY(complaint.createdAt)}
                      </Text>
                      {!!complaint.completedAt && (
                        <>
                          <MaterialCommunityIcons
                            name="check"
                            size={14}
                            color={theme.colors.success}
                            style={styles.completedIcon}
                          />
                          <Text style={styles.completedText}>
                            Completada: {formatDateDMY(complaint.completedAt)}
                          </Text>
                        </>
                      )}
                    </Row>
                  </Col>
                  <MaterialCommunityIcons
                    name="chevron-right"
                    size={theme.fontSizes.lg}
                    color={theme.colors.gray500}
                  />
                </Row>
              </Card>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filtersContainer: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  },
  filterButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radii.pill,
    borderWidth: 2,
    marginRight: theme.spacing.sm,
    backgroundColor: theme.colors.white,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  noDataCard: {
    margin: theme.spacing.lg,
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    fontStyle: "italic",
  },
  complaintCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  priorityIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  complaintInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  priorityChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
    marginRight: theme.spacing.sm,
  },
  priorityText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    textTransform: "uppercase",
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  complaintDetail: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.black,
    lineHeight: 18,
    marginBottom: theme.spacing.sm,
  },
  dateRow: {
    marginBottom: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
  completedIcon: {
    marginLeft: theme.spacing.md,
  },
  completedText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.success,
    marginLeft: 6,
  },
  //
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  viewAllRow: {
    justifyContent: "center",
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  viewAllText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },
});
