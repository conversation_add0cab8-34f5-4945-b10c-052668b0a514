import { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Me } from "../../interfaces/me";
import { Status } from "../../interfaces/maintenance-issue-report";
import { formatDateDMY } from "../../utils/date-time.utils";
import { Loading } from "../../components/Loading";
import { PropertyMaintenanceReportsRouteProp } from "../../navigation";
import { useRoute } from "@react-navigation/native";
import {
  getStatusColor,
  getStatusIcon,
  getStatusLabel,
} from "../../utils/convertions";

const getCategoryIcon = (
  description?: string
): keyof typeof MaterialCommunityIcons.glyphMap => {
  if (!description) return "wrench";

  const desc = description.toLowerCase();
  if (
    desc.includes("plomería") ||
    desc.includes("agua") ||
    desc.includes("tubería")
  ) {
    return "pipe";
  } else if (
    desc.includes("eléctrico") ||
    desc.includes("luz") ||
    desc.includes("electricidad")
  ) {
    return "lightning-bolt";
  } else if (
    desc.includes("pintura") ||
    desc.includes("pared") ||
    desc.includes("pintar")
  ) {
    return "format-paint";
  } else if (
    desc.includes("jardín") ||
    desc.includes("jardinería") ||
    desc.includes("plantas")
  ) {
    return "flower";
  } else if (desc.includes("limpieza") || desc.includes("limpiar")) {
    return "broom";
  } else if (
    desc.includes("seguridad") ||
    desc.includes("puerta") ||
    desc.includes("cerradura")
  ) {
    return "shield";
  } else if (
    desc.includes("aire") ||
    desc.includes("ventilación") ||
    desc.includes("clima")
  ) {
    return "air-conditioner";
  } else {
    return "wrench";
  }
};

const getCategoryColor = (description?: string): string => {
  if (!description) return theme.colors.secondary;

  const desc = description.toLowerCase();
  if (desc.includes("plomería") || desc.includes("agua")) {
    return theme.colors.primary;
  } else if (desc.includes("eléctrico") || desc.includes("luz")) {
    return theme.colors.warning;
  } else if (desc.includes("pintura") || desc.includes("pared")) {
    return theme.colors.success;
  } else if (desc.includes("jardín") || desc.includes("plantas")) {
    return theme.colors.success;
  } else if (desc.includes("seguridad") || desc.includes("puerta")) {
    return theme.colors.error;
  } else {
    return theme.colors.secondary;
  }
};

export const PropertyMaintenanceReportsScreen: React.FC = () => {
  const route = useRoute<PropertyMaintenanceReportsRouteProp>();
  const { maintenanceIssueReports: reports, property } = route.params || {};

  const [selectedFilter, setSelectedFilter] = useState<Status | "ALL">("ALL");

  const { data: userData, isLoading } = useCachedQuery<Me>(`mobile/me`);

  // Pon los hooks aquí, arriba de cualquier return
  const filteredReports = useMemo(() => {
    if (selectedFilter === "ALL") return reports;
    return reports.filter((report) => report.status === selectedFilter);
  }, [reports, selectedFilter]);

  // Estadísticas
  const openReports = reports.filter((r) => r.status === Status.OPEN);
  const inProgressReports = reports.filter(
    (r) => r.status === Status.IN_PROGRESS
  );
  const resolvedReports = reports.filter((r) => r.status === Status.RESOLVED);

  const filterOptions = [
    {
      key: "ALL",
      label: "Todos",
      count: reports.length,
      color: theme.colors.gray500,
    },
    {
      key: Status.OPEN,
      label: "Abiertos",
      count: openReports.length,
      color: theme.colors.primary,
    },
    {
      key: Status.IN_PROGRESS,
      label: "En Progreso",
      count: inProgressReports.length,
      color: theme.colors.warning,
    },
    {
      key: Status.RESOLVED,
      label: "Resueltos",
      count: resolvedReports.length,
      color: theme.colors.success,
    },
  ];

  // Después de todos los hooks, haz returns condicionales:
  if (isLoading) return <Loading />;
  if (!userData) return <Text>Sin datos</Text>;

  return (
    <GradientView
      firstLineText="Reportes de Mantenimiento"
      secondLineText={property?.address ?? "Mi Propiedad"}
    >
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Filtros */}
        <View style={styles.filtersContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {filterOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.filterButton,
                  selectedFilter === option.key && styles.filterButtonActive,
                  { borderColor: option.color },
                ]}
                onPress={() => setSelectedFilter(option.key as Status | "ALL")}
                activeOpacity={0.7}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    selectedFilter === option.key &&
                      styles.filterButtonTextActive,
                    {
                      color:
                        selectedFilter === option.key
                          ? theme.colors.white
                          : option.color,
                    },
                  ]}
                >
                  {option.label} ({option.count})
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Lista de reportes */}
        {filteredReports.length === 0 ? (
          <Card style={styles.noDataCard}>
            <Text style={styles.noDataText}>
              {selectedFilter === "ALL"
                ? "No hay reportes de mantenimiento registrados"
                : `No hay reportes ${filterOptions
                    .find((f) => f.key === selectedFilter)
                    ?.label.toLowerCase()}`}
            </Text>
          </Card>
        ) : (
          filteredReports.map((report) => (
            <TouchableOpacity
              key={report.id}
              onPress={() => console.log("Ver detalle de reporte:", report.id)}
              activeOpacity={0.7}
            >
              <Card style={styles.reportCard}>
                <Row align="flex-start">
                  <View
                    style={[
                      styles.categoryIndicator,
                      {
                        backgroundColor: `${getCategoryColor(
                          report.description
                        )}20`,
                      },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={getCategoryIcon(report.description)}
                      size={24}
                      color={getCategoryColor(report.description)}
                    />
                  </View>
                  <Col style={styles.reportInfo}>
                    <Row align="center" style={styles.headerRow}>
                      <Text style={styles.reportDescription} numberOfLines={2}>
                        {report.description}
                      </Text>
                      <View
                        style={[
                          styles.statusChip,
                          { backgroundColor: getStatusColor(report.status) },
                        ]}
                      >
                        <MaterialCommunityIcons
                          name={getStatusIcon(report.status)}
                          size={12}
                          color={theme.colors.white}
                        />
                        <Text style={styles.statusText}>
                          {getStatusLabel(report.status)}
                        </Text>
                      </View>
                    </Row>
                    <Row align="center" style={styles.dateRow}>
                      <MaterialCommunityIcons
                        name="calendar"
                        size={14}
                        color={theme.colors.gray500}
                      />
                      <Text style={styles.dateText}>
                        Creado: {formatDateDMY(report.createdAt)}
                      </Text>
                    </Row>
                  </Col>
                  <MaterialCommunityIcons
                    name="chevron-right"
                    size={theme.fontSizes.lg}
                    color={theme.colors.gray500}
                  />
                </Row>
              </Card>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </GradientView>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filtersContainer: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  },
  filterButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radii.pill,
    borderWidth: 2,
    marginRight: theme.spacing.sm,
    backgroundColor: theme.colors.white,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  noDataCard: {
    margin: theme.spacing.lg,
    padding: theme.spacing.xl,
    alignItems: "center",
  },
  noDataText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    fontStyle: "italic",
  },
  reportCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  categoryIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  reportInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
    alignItems: "flex-start",
  },
  reportDescription: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
    lineHeight: 20,
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "capitalize",
  },
  dateRow: {
    marginTop: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 6,
  },
});
