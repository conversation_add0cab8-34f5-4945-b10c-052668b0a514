import React, { useState, useMemo } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { GradientView } from "../../components/layouts/GradientView";
import { Card } from "../../components/main/Card";
import { Row } from "../../components/main/Row";
import { Col } from "../../components/main/Col";
import { theme } from "../../theme";
import { useCachedQuery } from "../../hooks/useCachedQueries";
import { Facility } from "../../interfaces/facility";
import { Loading } from "../../components/Loading";
import { QUERIES } from "../../constants/queries";
import { FacilitiesStackParamList } from "../../navigation/types";
import { FACILITIES_SCREENS } from "../../navigation/constants";
import { getStringTime, shortDaysOfWeek } from "../../utils/date-time.utils";

const getFacilityIcon = (
  facilityName: string
): keyof typeof MaterialCommunityIcons.glyphMap => {
  const name = facilityName.toLowerCase();
  if (
    name.includes("alberca") ||
    name.includes("piscina") ||
    name.includes("pool")
  ) {
    return "pool";
  } else if (name.includes("gimnasio") || name.includes("gym")) {
    return "dumbbell";
  } else if (
    name.includes("salon") ||
    name.includes("salón") ||
    name.includes("eventos")
  ) {
    return "party-popper";
  } else if (name.includes("cancha") || name.includes("tenis")) {
    return "tennis";
  } else if (
    name.includes("futbol") ||
    name.includes("fútbol") ||
    name.includes("soccer")
  ) {
    return "soccer";
  } else if (name.includes("basquet") || name.includes("basketball")) {
    return "basketball";
  } else if (
    name.includes("jardin") ||
    name.includes("jardín") ||
    name.includes("parque")
  ) {
    return "tree";
  } else if (
    name.includes("bbq") ||
    name.includes("asador") ||
    name.includes("parrilla")
  ) {
    return "grill";
  } else if (name.includes("juegos") || name.includes("niños")) {
    return "gamepad-variant";
  } else {
    return "home-city";
  }
};

const getAvailabilityStatus = (facility: Facility) => {
  const now = new Date();
  const currentDay = now.getDay();
  const currentTime = now.getHours() * 100 + now.getMinutes();

  // Verificar si está abierto hoy
  const isOpenToday = facility.daysOfWeek.includes(currentDay);

  if (!isOpenToday) {
    return {
      status: "closed",
      label: "Cerrado hoy",
      color: theme.colors.error,
    };
  }

  // Verificar horario
  const openTime = parseInt(facility.open.replace(":", ""));
  const closeTime = parseInt(facility.close.replace(":", ""));

  if (currentTime >= openTime && currentTime <= closeTime) {
    return {
      status: "open",
      label: "Abierto ahora",
      color: theme.colors.success,
    };
  } else {
    return { status: "closed", label: "Cerrado", color: theme.colors.error };
  }
};

export const FacilitiesScreenRedesigned: React.FC = () => {
  const { data, isLoading } = useCachedQuery<Facility[]>(QUERIES.FACILITIES);
  const navigation =
    useNavigation<NativeStackNavigationProp<FacilitiesStackParamList>>();

  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState<
    "all" | "reservable" | "open"
  >("all");

  const facilities = data ?? [];

  // Filtrar amenidades
  const filteredFacilities = useMemo(() => {
    let filtered = facilities;

    // Filtro por búsqueda
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (facility) =>
          facility.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          facility.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filtro por tipo
    if (selectedFilter === "reservable") {
      filtered = filtered.filter((facility) => facility.reservable);
    } else if (selectedFilter === "open") {
      filtered = filtered.filter((facility) => {
        const status = getAvailabilityStatus(facility);
        return status.status === "open";
      });
    }

    return filtered;
  }, [facilities, searchQuery, selectedFilter]);

  if (isLoading) {
    return <Loading />;
  }

  const reservableFacilities = facilities.filter((f) => f.reservable);
  const openFacilities = facilities.filter(
    (f) => getAvailabilityStatus(f).status === "open"
  );

  const filterOptions = [
    { key: "all", label: "Todas", count: facilities.length },
    {
      key: "reservable",
      label: "Reservables",
      count: reservableFacilities.length,
    },
    { key: "open", label: "Abiertas", count: openFacilities.length },
  ];

  return (
    <GradientView
      firstLineText="Amenidades"
      secondLineText="Descubre y reserva"
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Estadísticas rápidas */}
        <Card style={styles.statsCard}>
          <Row align="center" style={styles.statsRow}>
            <View style={styles.statItem}>
              <MaterialCommunityIcons
                name="home-city"
                size={24}
                color={theme.colors.primary}
              />
              <Text style={styles.statNumber}>{facilities.length}</Text>
              <Text style={styles.statLabel}>Total</Text>
            </View>
            <View style={styles.statItem}>
              <MaterialCommunityIcons
                name="calendar-check"
                size={24}
                color={theme.colors.success}
              />
              <Text style={styles.statNumber}>
                {reservableFacilities.length}
              </Text>
              <Text style={styles.statLabel}>Reservables</Text>
            </View>
            <View style={styles.statItem}>
              <MaterialCommunityIcons
                name="clock-check"
                size={24}
                color={theme.colors.warning}
              />
              <Text style={styles.statNumber}>{openFacilities.length}</Text>
              <Text style={styles.statLabel}>Abiertas</Text>
            </View>
          </Row>
        </Card>

        {/* Barra de búsqueda */}
        <Card style={styles.searchCard}>
          <Row align="center" style={styles.searchContainer}>
            <MaterialCommunityIcons
              name="magnify"
              size={20}
              color={theme.colors.gray500}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar amenidades..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={theme.colors.gray500}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <MaterialCommunityIcons
                  name="close-circle"
                  size={20}
                  color={theme.colors.gray500}
                />
              </TouchableOpacity>
            )}
          </Row>
        </Card>

        {/* Filtros */}
        <Card style={styles.filtersCard}>
          <Text style={styles.filtersTitle}>Filtrar por</Text>
          <Row style={styles.filtersRow}>
            {filterOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.filterChip,
                  selectedFilter === option.key && styles.activeFilterChip,
                ]}
                onPress={() => setSelectedFilter(option.key as any)}
                activeOpacity={0.7}
              >
                <Text
                  style={[
                    styles.filterText,
                    selectedFilter === option.key && styles.activeFilterText,
                  ]}
                >
                  {option.label} ({option.count})
                </Text>
              </TouchableOpacity>
            ))}
          </Row>
        </Card>

        {/* Lista de amenidades */}
        {filteredFacilities.length === 0 ? (
          <Card style={styles.noDataCard}>
            <MaterialCommunityIcons
              name="home-search"
              size={48}
              color={theme.colors.gray500}
            />
            <Text style={styles.noDataText}>
              {searchQuery.trim()
                ? "No se encontraron amenidades que coincidan con tu búsqueda"
                : "No hay amenidades disponibles"}
            </Text>
          </Card>
        ) : (
          filteredFacilities.map((facility) => {
            const availabilityStatus = getAvailabilityStatus(facility);
            const facilityIcon = getFacilityIcon(facility.name);

            return (
              <TouchableOpacity
                key={facility.id}
                onPress={() => {
                  navigation.navigate(FACILITIES_SCREENS.FACILITY_DETAIL, {
                    facility,
                  });
                }}
                activeOpacity={0.8}
              >
                <Card style={styles.facilityCard}>
                  <Row align="flex-start">
                    {/* Imagen con overlay de icono */}
                    <View style={styles.imageContainer}>
                      <Image
                        source={{ uri: facility.imagePath }}
                        style={styles.facilityImage}
                        defaultSource={require("../../assets/icon.png")}
                      />
                      <LinearGradient
                        colors={["transparent", "rgba(0,0,0,0.7)"]}
                        style={styles.imageOverlay}
                      >
                        <MaterialCommunityIcons
                          name={facilityIcon}
                          size={24}
                          color={theme.colors.white}
                        />
                      </LinearGradient>
                    </View>

                    {/* Información de la amenidad */}
                    <Col style={styles.facilityInfo}>
                      <Row align="center" style={styles.headerRow}>
                        <Text style={styles.facilityName} numberOfLines={1}>
                          {facility.name}
                        </Text>
                        <View
                          style={[
                            styles.statusBadge,
                            { backgroundColor: availabilityStatus.color },
                          ]}
                        >
                          <Text style={styles.statusText}>
                            {availabilityStatus.label}
                          </Text>
                        </View>
                      </Row>

                      <Text
                        style={styles.facilityDescription}
                        numberOfLines={2}
                      >
                        {facility.description}
                      </Text>

                      {/* Horarios */}
                      <Row align="center" style={styles.scheduleRow}>
                        <MaterialCommunityIcons
                          name="clock-outline"
                          size={14}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.scheduleText}>
                          {getStringTime(facility.open)} -{" "}
                          {getStringTime(facility.close)}
                        </Text>
                      </Row>

                      {/* Días disponibles */}
                      <Row align="center" style={styles.daysRow}>
                        <MaterialCommunityIcons
                          name="calendar-outline"
                          size={14}
                          color={theme.colors.gray500}
                        />
                        <Text style={styles.daysText}>
                          {facility.daysOfWeek.length
                            ? facility.daysOfWeek
                                .map((day) => shortDaysOfWeek[day])
                                .join(", ")
                            : "Sin horario definido"}
                        </Text>
                      </Row>

                      {/* Información adicional */}
                      <Row align="center" style={styles.extraInfoRow}>
                        {facility.reservable && (
                          <View style={styles.featureTag}>
                            <MaterialCommunityIcons
                              name="calendar-check"
                              size={12}
                              color={theme.colors.success}
                            />
                            <Text style={styles.featureText}>Reservable</Text>
                          </View>
                        )}
                        {facility.maxAmountOfPeople && (
                          <View style={styles.featureTag}>
                            <MaterialCommunityIcons
                              name="account-group"
                              size={12}
                              color={theme.colors.primary}
                            />
                            <Text style={styles.featureText}>
                              Máx. {facility.maxAmountOfPeople}
                            </Text>
                          </View>
                        )}
                      </Row>
                    </Col>

                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={theme.fontSizes.lg}
                      color={theme.colors.gray500}
                    />
                  </Row>
                </Card>
              </TouchableOpacity>
            );
          })
        )}
      </ScrollView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  statsCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  statsRow: {
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  statLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  searchCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  searchContainer: {
    backgroundColor: theme.colors.gray100,
    borderRadius: theme.radii.lg,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  searchIcon: {
    marginRight: theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: theme.fontSizes.md,
    color: theme.colors.black,
    paddingVertical: 0,
  },
  filtersCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  filtersTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.md,
  },
  filtersRow: {
    flexWrap: "wrap",
    gap: theme.spacing.sm,
  },
  filterChip: {
    backgroundColor: theme.colors.gray100,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.radii.lg,
    borderWidth: 1,
    borderColor: theme.colors.gray200,
  },
  activeFilterChip: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    fontWeight: "500",
  },
  activeFilterText: {
    color: theme.colors.white,
    fontWeight: "600",
  },
  noDataCard: {
    alignItems: "center",
    paddingVertical: theme.spacing.xl,
  },
  noDataText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: theme.spacing.md,
    lineHeight: 22,
  },
  facilityCard: {
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  imageContainer: {
    position: "relative",
    marginRight: theme.spacing.md,
  },
  facilityImage: {
    width: 80,
    height: 80,
    borderRadius: theme.radii.lg,
    backgroundColor: theme.colors.gray200,
  },
  imageOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 30,
    borderBottomLeftRadius: theme.radii.lg,
    borderBottomRightRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
  },
  facilityInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.xs,
  },
  facilityName: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
  },
  facilityDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    lineHeight: 18,
    marginBottom: theme.spacing.sm,
  },
  scheduleRow: {
    marginBottom: theme.spacing.xs,
  },
  scheduleText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
    fontWeight: "500",
  },
  daysRow: {
    marginBottom: theme.spacing.sm,
  },
  daysText: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginLeft: theme.spacing.xs,
  },
  extraInfoRow: {
    flexWrap: "wrap",
    gap: theme.spacing.sm,
  },
  featureTag: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: theme.colors.gray100,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  featureText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray600,
    marginLeft: 2,
    fontWeight: "500",
  },
});
